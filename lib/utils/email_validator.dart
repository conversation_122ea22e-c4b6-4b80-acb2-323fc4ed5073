class EmailValidator {
  /// Validates email addresses including support for the "+" symbol
  /// which is commonly used for email aliasing (e.g., <EMAIL>)
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter an email address';
    }

    // Remove leading and trailing whitespace
    value = value.trim();

    // Basic email regex that supports the "+" symbol
    // This regex allows:
    // - Letters, numbers, dots, hyphens, underscores, and plus signs in the local part
    // - Standard domain format
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    // Additional checks for common email format issues
    if (value.startsWith('.') || value.endsWith('.')) {
      return 'Email cannot start or end with a dot';
    }

    if (value.contains('..')) {
      return 'Email cannot contain consecutive dots';
    }

    if (value.startsWith('@') || value.endsWith('@')) {
      return 'Invalid email format';
    }

    // Check if there's exactly one @ symbol
    final atCount = value.split('@').length - 1;
    if (atCount != 1) {
      return 'Email must contain exactly one @ symbol';
    }

    // Check local part (before @) doesn't start or end with dot or special characters
    final parts = value.split('@');
    final localPart = parts[0];
    if (localPart.startsWith('.') || localPart.endsWith('.')) {
      return 'Email local part cannot start or end with a dot';
    }

    // Check local part doesn't end with + (plus sign should have text after it)
    if (localPart.endsWith('+')) {
      return 'Email local part cannot end with a plus sign';
    }

    return null; // Email is valid
  }

  /// Validates email with Arabic error messages
  static String? validateEmailArabic(String? value) {
    if (value == null || value.isEmpty) {
      return 'رجاء أدخل الإيميل';
    }

    // Remove leading and trailing whitespace
    value = value.trim();

    // Basic email regex that supports the "+" symbol
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(value)) {
      return 'رجاء أدخل إيميل صحيح';
    }

    // Additional checks for common email format issues
    if (value.startsWith('.') || value.endsWith('.')) {
      return 'الإيميل لا يمكن أن يبدأ أو ينتهي بنقطة';
    }

    if (value.contains('..')) {
      return 'الإيميل لا يمكن أن يحتوي على نقاط متتالية';
    }

    if (value.startsWith('@') || value.endsWith('@')) {
      return 'تنسيق الإيميل غير صحيح';
    }

    // Check if there's exactly one @ symbol
    final atCount = value.split('@').length - 1;
    if (atCount != 1) {
      return 'الإيميل يجب أن يحتوي على رمز @ واحد فقط';
    }

    // Check local part (before @) doesn't start or end with dot
    final parts = value.split('@');
    final localPart = parts[0];
    if (localPart.startsWith('.') || localPart.endsWith('.')) {
      return 'الجزء المحلي من الإيميل لا يمكن أن يبدأ أو ينتهي بنقطة';
    }

    // Check local part doesn't end with + (plus sign should have text after it)
    if (localPart.endsWith('+')) {
      return 'الجزء المحلي من الإيميل لا يمكن أن ينتهي برمز +';
    }

    return null; // Email is valid
  }
}
