import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/utils/email_validator.dart';
import 'package:busaty_parents/views/custom_widgets/custom_button.dart';
import 'package:busaty_parents/views/custom_widgets/custom_form_field_border.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/widgets/custom_background_image.dart';

import '../../../bloc/cubit/register_cubit/register_cubit.dart';
import '../../../bloc/cubit/register_cubit/register_state.dart';
import '../../../config/global_variable.dart';
import '../../../helper/response_state.dart';
import '../send_code_screen/send_code_screen.dart';

class SignupScreen extends StatefulWidget {
  static const String routeName = PathRouteName.signup;
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  bool securityCheck = true;
  bool securityCheck1 = true;
  String? address;
  String? name;
  String? password;
  String? email;
  String? confirmedPassword;
  String? phone;

  var image;

  Future<void> getImage() async {
    try {
      final images = await ImagePicker().pickImage(source: ImageSource.gallery);
      final imageFile = File(images!.path);
      setState(() {
        image = imageFile;
      });
    } on PlatformException catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint(e.toString());
    }
  }

  @override
  void initState() {
    super.initState();
    // Fetch user status when the screen is initialized
    fetchUserStatus();
  }

  Future<void> fetchUserStatus() async {
    // Assuming you have a default name or you can pass the name as needed
    String defaultName = "parents";
    await context.read<RegisterCubit>().getUserStatus(defaultName);
    // Handle the status as needed
    debugPrint("User Status: ${context.read<RegisterCubit>().getStatus}");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const Sbox(h: 10),
              const Sbox(h: 20),
              CustomText(
                text: AppStrings.signup.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const Sbox(h: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        const Sbox(h: 40),
                        CustomFormFieldWithBorder(
                          saved: (value) {
                            name = value;
                          },
                          validation: AppStrings.name.tr(),
                          contentPaddingVertical: 0,
                          inputType: TextInputType.name,
                          prefix: const Icon(
                            Icons.person_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          hintText: AppStrings.name.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          requiredNumber: 4,
                        ),
                        const Sbox(h: 15),
                        CustomFormFieldWithBorder(
                          saved: (value) {
                            email = value;
                          },
                          validation: AppStrings.validEmail.tr(),
                          inputType: TextInputType.emailAddress,
                          customValidator: (value) {
                            // Use Arabic validation if the current locale is Arabic
                            if (context.locale.toString() == "ar") {
                              return EmailValidator.validateEmailArabic(value);
                            } else {
                              return EmailValidator.validateEmail(value);
                            }
                          },
                          prefix: const Icon(
                            Icons.mail_outline_outlined,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          hintText: AppStrings.email.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                        ),
                        const Sbox(h: 15),
                        if (context.read<RegisterCubit>().getStatus == true)
                          CustomFormFieldWithBorder(
                            saved: (value) {
                              phone = value;
                            },
                            validation: AppStrings.validPhone.tr(),
                            inputType: TextInputType.phone,
                            requiredNumber: 10,
                            prefix: const Icon(
                              Icons.phone_outlined,
                              color: TColor.iconInputColor,
                            ),
                            formFieldWidth: 307,
                            hintText: AppStrings.phoneNumber.tr(),
                            borderColor: TColor.fillFormFieldB,
                            fillColor: TColor.fillFormFieldB,
                            radiusNumber: 15.0,
                          ),
                        if (context.read<RegisterCubit>().getStatus == true)
                          const Sbox(h: 15),
                        if (context.read<RegisterCubit>().getStatus == true)
                          CustomFormFieldWithBorder(
                            saved: (value) {
                              address = value;
                            },
                            validation: AppStrings.validAddress.tr(),
                            inputType: TextInputType.name,
                            requiredNumber: 5,
                            prefix: const Icon(
                              Icons.location_on_outlined,
                              color: TColor.iconInputColor,
                            ),
                            formFieldWidth: 307,
                            hintText: AppStrings.address.tr(),
                            borderColor: TColor.fillFormFieldB,
                            fillColor: TColor.fillFormFieldB,
                            radiusNumber: 15.0,
                          ),
                        if (context.read<RegisterCubit>().getStatus == true)
                          const Sbox(h: 15),
                        CustomFormFieldWithBorder(
                          requiredNumber: 8,
                          saved: (value) {
                            password = value;
                          },
                          validation: AppStrings.validPassword.tr(),
                          security: securityCheck,
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          hintText: AppStrings.password.tr(),
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck = !securityCheck;
                              });
                            },
                            child: securityCheck
                                ? const Icon(
                                    Icons.visibility_off,
                                    color: TColor.iconInputColor,
                                  )
                                : const Icon(
                                    Icons.visibility_outlined,
                                    color: TColor.iconInputColor,
                                  ),
                          ),
                        ),
                        const Sbox(h: 15),
                        CustomFormFieldWithBorder(
                          saved: (value) {
                            confirmedPassword = value;
                          },
                          validation: AppStrings.validConfirmPassword.tr(),
                          security: securityCheck1,
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          hintText: AppStrings.confirmPassword.tr(),
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck1 = !securityCheck1;
                              });
                            },
                            child: securityCheck1
                                ? const Icon(
                                    Icons.visibility_off,
                                    color: TColor.iconInputColor,
                                  )
                                : const Icon(
                                    Icons.visibility_outlined,
                                    color: TColor.iconInputColor,
                                  ),
                          ),
                        ),
                        const Sbox(h: 50),
                        BlocConsumer<RegisterCubit, RegisterState>(
                          listener: (context, states) async {
                            if (states.rState == ResponseState.success) {
                              tempToken = states.registerModel!.token;

                              // Cache the authentication method as regular for new user registration
                              await CacheHelper.putString(
                                  authMethodKey, authMethodRegular);
                              authMethod = authMethodRegular;

                              // Add debug logging for signup (regular authentication)
                              Logger.i(
                                  "Authentication method cached during signup: Regular");
                              Logger.d(
                                  "Auth method value stored in cache: ${CacheHelper.getString(authMethodKey)}");
                              Logger.d(
                                  "Auth method global variable value: $authMethod");

                              if (!mounted) return;

                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  backgroundColor: TColor.greenSuccess,
                                  content: CustomText(
                                    text: AppStrings.checkEmail.tr(),
                                    color: TColor.white,
                                    maxLine: 3,
                                  ),
                                ),
                              );
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      SendCodeScreen(email: email),
                                ),
                              );
                            } else if (states.rState == ResponseState.failure) {
                              debugPrint(
                                  "Error:: states.registerModels?.massage = ${states.registerModel?.message.toString()}");
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text: context.locale.toString() == "ar"
                                        ? states.rMessage
                                            ?.replaceAll(
                                                'The email has already been taken',
                                                'هذا البريد مسجل بالفعل')
                                            .replaceAll(
                                                'The phone has already been taken',
                                                'هذا الهاتف مسجل بالفعل')
                                        : states.rMessage,
                                    color: TColor.white,
                                    maxLine: 3,
                                  ),
                                ),
                              );
                            }
                          },
                          builder: (context, states) {
                            return states.rState == ResponseState.loading
                                ? const Center(
                                    child: CircularProgressIndicator(
                                      color: TColor.mainColor,
                                    ),
                                  )
                                : CustomButton(
                                    text: AppStrings.signup.tr(),
                                    onTap: () async {
                                      if (_formKey.currentState!.validate()) {
                                        _formKey.currentState!.save();
                                        context.read<RegisterCubit>().register(
                                              name: name,
                                              phone: phone,
                                              email: email,
                                              password: password,
                                              confirmedPassword:
                                                  confirmedPassword,
                                              address: address,
                                            );
                                      }
                                    },
                                    width: 307,
                                    height: 48,
                                    radius: 15,
                                    borderColor: TColor.mainColor,
                                    bgColor: TColor.mainColor,
                                  );
                          },
                        ),
                        const Sbox(h: 40),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomText(
                              text: AppStrings.haveAccount.tr(),
                              fontSize: 16,
                              fontW: FontWeight.w500,
                              color: TColor.textLogin,
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: CustomText(
                                text: AppStrings.login.tr(),
                                fontSize: 16,
                                fontW: FontWeight.w500,
                                color: TColor.mainColor,
                              ),
                            ),
                          ],
                        ),
                        const Sbox(h: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
