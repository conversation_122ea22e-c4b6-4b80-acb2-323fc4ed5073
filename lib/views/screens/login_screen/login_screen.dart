import 'dart:io';

import 'package:busaty_parents/deep_link.dart';
import 'package:busaty_parents/views/screens/complete_profile_screen/complete_profile_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/bloc/cubit/login_cubit/login_cubit.dart';
import 'package:busaty_parents/bloc/cubit/login_cubit/login_state.dart';
import 'package:busaty_parents/config/global_variable.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/helper/response_state.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/utils/assets_utils.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/utils/email_validator.dart';
import 'package:busaty_parents/views/custom_widgets/custom_button.dart';
import 'package:busaty_parents/views/custom_widgets/custom_form_field_border.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/views/screens/forget_password_screen/forget_password_screen.dart';
import 'package:busaty_parents/views/screens/home_screen/home_screen.dart';
import 'package:busaty_parents/views/screens/send_code_screen/send_code_screen.dart';
import 'package:busaty_parents/widgets/custom_background_image.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:firebase_auth/firebase_auth.dart';

class LoginScreen extends StatefulWidget {
  static const String routeName = PathRouteName.login;

  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  @override
  void initState() {
    initializeFCM();

    super.initState();
  }

  bool _isPasswordHidden = true;
  bool _isRemembered = true;
  String? _email;
  String? _password;
  final _formKey = GlobalKey<FormState>();

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordHidden = !_isPasswordHidden;
    });
  }

  void _navigateToForgetPassword() {
    Navigator.pushNamedAndRemoveUntil(
        context, ForgetPasswordScreen.routeName, (route) => false);
  }

  void _navigateToSignUp() {
    Navigator.pushNamed(context, PathRouteName.signup);
  }

  Future<void> _onLoginSuccess(LoginState state) async {
    if (_isRemembered) {
      await CacheHelper.putString("token", state.loginModels!.token!);
    }
    token = state.loginModels!.token;

    // Cache the authentication method as regular
    // This method is only called for regular email/password login
    await CacheHelper.putString(authMethodKey, authMethodRegular);
    authMethod = authMethodRegular;

    // Add debug logging for regular authentication
    Logger.i("Authentication method cached: Regular");
    Logger.d(
        "Auth method value stored in cache: ${CacheHelper.getString(authMethodKey)}");
    Logger.d("Auth method global variable value: $authMethod");

    if (!mounted) return;

    if (state.loginModels?.identity_preview == false) {
      Navigator.pushReplacementNamed(context, CompleteProfileScreen.routeName);
    } else {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (_) => const HomeScreen()),
      );
    }
  }

  void _onLoginFailure() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "please enter correct email or password",
          color: TColor.white,
        ),
      ),
    );
  }

  void _onNeedVerification() {
    Navigator.pushReplacementNamed(context, SendCodeScreen.routeName);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "please verify your email",
          color: TColor.white,
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Column(
      children: [
        const Sbox(h: 10),
        Image.asset(
          assetsImages("logo.png"),
          height: 121.w,
          width: 124.w,
        ),
        CustomText(
          text: tr('Busaty - Parents'),
          color: TColor.white,
          fontSize: 20,
        ),
        const Sbox(h: 40),
      ],
    );
  }

  Widget _buildForm() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30.r),
        color: TColor.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 3,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      width: 354.w,
      padding: EdgeInsets.symmetric(vertical: 40.h, horizontal: 24.w),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            _buildEmailField(),
            const Sbox(h: 15),
            _buildPasswordField(),
            const Sbox(h: 15),
            _buildRememberAndForgetPassword(),
            const Sbox(h: 50),
            _buildLoginButton(),
            const Sbox(h: 40),
            _buildSignUpPrompt(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    return CustomFormFieldWithBorder(
      onChanged: (value) => _email = value,
      prefix: const Icon(
        Icons.mail_outline_outlined,
        color: TColor.iconInputColor,
      ),
      formFieldWidth: 307,
      hintText: AppStrings.email.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      validation: AppStrings.email.tr(),
      customValidator: (value) {
        // Use Arabic validation if the current locale is Arabic
        if (context.locale.toString() == "ar") {
          return EmailValidator.validateEmailArabic(value);
        } else {
          return EmailValidator.validateEmail(value);
        }
      },
      requiredNumber: 1,
      radiusNumber: 15.0,
    );
  }

  Widget _buildPasswordField() {
    return CustomFormFieldWithBorder(
      onChanged: (value) => _password = value,
      prefix: const Icon(
        Icons.lock_outline,
        color: TColor.iconInputColor,
      ),
      formFieldWidth: 307,
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      validation: AppStrings.password.tr(),
      requiredNumber: 1,
      security: _isPasswordHidden,
      hintText: AppStrings.password.tr(),
      suffix: InkWell(
        onTap: _togglePasswordVisibility,
        child: Icon(
          _isPasswordHidden ? Icons.visibility_off : Icons.visibility_outlined,
          color: TColor.iconInputColor,
        ),
      ),
    );
  }

  Widget _buildRememberAndForgetPassword() {
    return SizedBox(
      width: 307.w,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: _navigateToForgetPassword,
            child: CustomText(
              text: AppStrings.forgetPassword.tr(),
              color: TColor.textLogin,
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              CustomText(
                text: AppStrings.remember.tr(),
                color: TColor.textLogin,
                fontSize: 14,
              ),
              SizedBox(
                width: 24.w,
                height: 24.h,
                child: Checkbox(
                  activeColor: TColor.mainColor,
                  value: _isRemembered,
                  onChanged: (value) {
                    setState(() {
                      _isRemembered = value ?? false;
                    });
                  },
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoginButton() {
    return Column(
      children: [
        BlocConsumer<LoginCubit, LoginState>(
          listener: (context, state) {
            switch (state.rStates) {
              case ResponseState.success:
                _onLoginSuccess(state);
                break;
              case ResponseState.failure:
                _onLoginFailure();
                break;
              case ResponseState.needVerivecation:
                _onNeedVerification();
                break;
              default:
                break;
            }
          },
          builder: (context, state) {
            if (state.rStates == ResponseState.loading) {
              return const CircularProgressIndicator(
                color: TColor.mainColor,
              );
            }

            return CustomButton(
              text: AppStrings.login.tr(),
              onTap: _handleLogin,
              width: 307,
              height: 48,
              radius: 15,
              borderColor: TColor.mainColor,
              bgColor: TColor.mainColor,
            );
          },
        ),
        const Sbox(h: 20),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 1,
                color: TColor.textLogin.withOpacity(0.3),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: CustomText(
                text: AppStrings.or.tr(),
                color: TColor.textLogin,
                fontSize: 14,
              ),
            ),
            Expanded(
              child: Container(
                height: 1,
                color: TColor.textLogin.withOpacity(0.3),
              ),
            ),
          ],
        ),
        const Sbox(h: 20),
        _buildGoogleButton(),
      ],
    );
  }

  void _handleLogin() {
    if (_formKey.currentState?.validate() ?? false) {
      _formKey.currentState?.save();
      context.read<LoginCubit>().login(
            email: _email,
            password: _password,
          );
    }
  }

  Widget _buildGoogleButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => handleGoogleLogin(context),
        borderRadius: BorderRadius.circular(15),
        child: Container(
          width: 307.w,
          height: 48.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                "assets/images/google_icon.png",
                width: 24.w,
                height: 24.h,
              ),
              SizedBox(width: 12.w),
              Text(
                AppStrings.loginWithGoogle.tr(),
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> handleGoogleLogin(BuildContext context) async {
    try {
      debugPrint('=== Google Sign In Debug ===');
      debugPrint('Initializing GoogleSignIn...');

      final GoogleSignIn googleSignIn = GoogleSignIn(
          scopes: ['email'],
          signInOption: SignInOption.standard,
          clientId:
              '************-j89kvjdhljgjkpi491km7qdu7rkgj4or.apps.googleusercontent.com');

      // Clear any existing sign in
      debugPrint('Signing out from any existing session...');
      await googleSignIn.signOut();

      debugPrint('Starting sign in flow...');
      final GoogleSignInAccount? googleUser =
          await googleSignIn.signIn().catchError((error) {
        debugPrint('Error during signIn: $error');
        throw error;
      });

      if (googleUser == null) {
        debugPrint('Sign in aborted by user');
        return;
      }

      debugPrint(
          'Successfully signed in with Google. Email: ${googleUser.email}');
      debugPrint('Getting auth tokens...');

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication.catchError((error) {
        debugPrint('Error getting authentication: $error');
        throw error;
      });

      debugPrint(
          'Got auth tokens. AccessToken length: ${googleAuth.accessToken?.length}');
      debugPrint('IdToken length: ${googleAuth.idToken?.length}');

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      debugPrint('Getting Firebase user credential...');
      final userCredential = await FirebaseAuth.instance
          .signInWithCredential(credential)
          .catchError((error) {
        debugPrint('Firebase Auth error: $error');
        throw error;
      });

      final idToken = await userCredential.user?.getIdToken();
      debugPrint('Firebase ID token obtained: ${idToken != null}');

      if (idToken == null) {
        throw Exception('Failed to get Firebase ID token');
      }

      // CRITICAL FIX: Make sure SharedPreferences is initialized
      await CacheHelper.init();

      // Cache the authentication method as Google BEFORE making the API call
      // This ensures it's set even if there's an issue with the API call
      Logger.i("CRITICAL: Setting auth_method to 'google'");
      final result =
          await CacheHelper.putString(authMethodKey, authMethodGoogle);
      Logger.i("CRITICAL: Result of setting auth_method: $result");
      authMethod = authMethodGoogle;

      // Add debug logging for Google authentication
      Logger.i("Authentication method cached EARLY: Google");
      Logger.d(
          "Auth method value stored in cache EARLY: ${CacheHelper.getString(authMethodKey)}");
      Logger.d("Auth method global variable value EARLY: $authMethod");

      // Force debug output to console
      Logger.i("GOOGLE LOGIN: USER IS BEING AUTHENTICATED WITH GOOGLE");
      Logger.i("GOOGLE LOGIN: AUTH METHOD KEY = $authMethodKey");
      Logger.i("GOOGLE LOGIN: AUTH METHOD VALUE = $authMethodGoogle");

      if (!mounted) return;

      // Store a reference to the LoginCubit before the async gap
      final loginCubit = context.read<LoginCubit>();

      debugPrint('Calling backend API...');

      // Use a try-catch block to handle any errors during the async operation
      try {
        await loginCubit.firebaseLogin(
            idToken: idToken, accessToken: googleAuth.accessToken);
      } catch (e) {
        if (mounted) {
          Logger.e("Error during firebaseLogin", e);

          // Use a post-frame callback to ensure we're not using context across async gaps
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (!mounted) return;

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                backgroundColor: TColor.redAccent,
                content: CustomText(
                  text: "Error during login: ${e.toString()}",
                  color: TColor.white,
                ),
              ),
            );
          });
        }
        rethrow;
      }

      if (!mounted) return;
      final loginState = loginCubit.state;
      debugPrint('Login state: ${loginState.rStates}');

      if (loginState.rStates == ResponseState.success) {
        // CRITICAL FIX: Clear any existing auth method first
        await CacheHelper.remove(authMethodKey);

        // Cache the authentication method as Google AGAIN to be sure
        Logger.i(
            "CRITICAL: Setting auth_method to 'google' AGAIN after successful login");
        final result2 =
            await CacheHelper.putString(authMethodKey, authMethodGoogle);
        Logger.i("CRITICAL: Result of setting auth_method AGAIN: $result2");
        authMethod = authMethodGoogle;

        // Verify the auth method was set correctly
        final verifiedAuthMethod = CacheHelper.getString(authMethodKey);
        Logger.i("VERIFIED auth method in cache: $verifiedAuthMethod");

        if (verifiedAuthMethod != authMethodGoogle) {
          Logger.e(
              "AUTH METHOD MISMATCH! Expected 'google', got '$verifiedAuthMethod'");
          // Force set it again
          await CacheHelper.putString(authMethodKey, authMethodGoogle);
          Logger.i("FORCED auth method to 'google' again");
        }

        // Add debug logging for Google authentication
        Logger.i("Authentication method cached: Google");
        Logger.d(
            "Auth method value stored in cache: ${CacheHelper.getString(authMethodKey)}");
        Logger.d("Auth method global variable value: $authMethod");

        // Continue with normal login success flow
        if (_isRemembered) {
          await CacheHelper.putString("token", loginState.loginModels!.token!);
        }
        token = loginState.loginModels!.token;

        if (!mounted) return;

        // Use a post-frame callback to ensure we're not using context across async gaps
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;

          if (loginState.loginModels?.identity_preview == false) {
            Navigator.pushReplacementNamed(
                context, CompleteProfileScreen.routeName);
          } else {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (_) => const HomeScreen()),
            );
          }
        });
      } else if (loginState.rStates == ResponseState.needVerivecation) {
        _onNeedVerification();
      }
    } catch (error, stackTrace) {
      debugPrint('=== Google Sign In Error ===');
      debugPrint('Error: $error');
      debugPrint('Stack trace: $stackTrace');

      if (!mounted) return;

      // Use a post-frame callback to ensure we're not using context across async gaps
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.redAccent,
            content: CustomText(
              text: _getSignInErrorMessage(error),
              color: TColor.white,
            ),
          ),
        );
      });
    }
  }

  String _getSignInErrorMessage(dynamic error) {
    if (error.toString().contains('network_error')) {
      return AppStrings.checkConnection.tr();
    } else if (error.toString().contains('sign_in_canceled')) {
      return 'Sign in was canceled';
    } else if (error.toString().contains('sign_in_failed')) {
      return 'Sign in failed. Please try again';
    }
    return AppStrings.loginFailed.tr();
  }

  // Future<UserCredential?> signInWithGoogle() async {
  //   try {
  //     final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
  //
  //     if (googleUser == null) return null;
  //
  //     final GoogleSignInAuthentication googleAuth =
  //         await googleUser.authentication;
  //     final credential = GoogleAuthProvider.credential(
  //       accessToken: googleAuth.accessToken,
  //       idToken: googleAuth.idToken,
  //     );
  //
  //     return await FirebaseAuth.instance.signInWithCredential(credential);
  //   } catch (e) {
  //     print('Google Sign In error: $e');
  //     return null;
  //   }
  // }

  Widget _buildSignUpPrompt() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CustomText(
          text: AppStrings.notHaveAccount.tr(),
          fontSize: 16,
          color: TColor.textLogin,
        ),
        InkWell(
          onTap: _navigateToSignUp,
          child: CustomText(
            text: AppStrings.createAccount.tr(),
            fontSize: 16,
            color: TColor.mainColor,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              _buildLogo(),
              CustomText(
                text: AppStrings.login.tr(),
                color: TColor.white,
                fontSize: 20,
              ),
              const Sbox(h: 30),
              _buildForm(),
            ],
          ),
        ),
      ),
    );
  }
}
