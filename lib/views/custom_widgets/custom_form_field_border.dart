import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';

class CustomFormFieldWithBorder extends StatelessWidget {
  final String? hintText;
  final bool security;
  final TextInputType inputType;
  final String? validation;
  final Function(dynamic)? saved;
  final int maxLine;
  final Widget? prefix;
  final Widget? suffix;
  final double? radiusNumber;
  final Function(String)? onChanged;
  final Function()? onComplete;
  final int? requiredNumber;
  final bool isTitled;
  final double? paddingLeft;
  final double? paddingRight;
  final double? formFieldWidth;
  final double? contentPaddingVertical;
  final double? contentPaddingHorizontal;
  final Color? fillColor;
  final Color? borderColor;
  final bool? height;
  final double? heightA;
  final TextEditingController? controller;
  final String? title;
  final Color? titleColor;
  final bool readOnly;
  final String? Function(String?)? customValidator;

  const CustomFormFieldWithBorder({
    this.isTitled = false,
    this.controller,
    this.onComplete,
    super.key,
    this.hintText,
    this.inputType = TextInputType.text,
    this.saved,
    this.validation,
    this.security = false,
    this.maxLine = 1,
    this.prefix,
    this.suffix,
    this.radiusNumber = 5,
    this.onChanged,
    this.fillColor = TColor.fillFormField,
    this.paddingLeft = 20,
    this.paddingRight = 20,
    this.formFieldWidth = 328,
    this.contentPaddingVertical = 0,
    this.contentPaddingHorizontal = 10,
    this.borderColor = TColor.black,
    this.height = false,
    this.heightA = 48,
    this.requiredNumber = 0,
    this.title,
    this.titleColor,
    this.readOnly = false,
    this.customValidator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isTitled
            ? Padding(
                padding: context.locale.toString() == "ar"
                    ? EdgeInsets.only(
                        left: paddingLeft!.w,
                        right: paddingRight!.w,
                        bottom: 5.h)
                    : EdgeInsets.only(
                        left: paddingLeft!.w,
                        right: paddingRight!.w,
                        bottom: 5.h),
                child: CustomText(
                  text: title,
                  fontSize: 15,
                  textAlign: TextAlign.center,
                  fontW: FontWeight.bold,
                  color: titleColor ?? TColor.black,
                ),
              )
            : const SizedBox(),
        Padding(
          padding: context.locale.toString() == "ar"
              ? EdgeInsets.only(left: paddingLeft!.w, right: paddingRight!.w)
              : EdgeInsets.only(left: paddingLeft!.w, right: paddingRight!.w),
          child: SizedBox(
            width: formFieldWidth!.w,
            // height: height == true ? 46.21.w : heightA!.w,
            child: TextFormField(
              readOnly: readOnly,
              controller: controller,
              onEditingComplete: onComplete,
              onChanged: onChanged,
              decoration: InputDecoration(
                prefixIcon: prefix,
                suffixIcon: suffix,
                filled: true,
                fillColor: fillColor,
                contentPadding: EdgeInsets.symmetric(
                    vertical: contentPaddingVertical!,
                    horizontal: contentPaddingHorizontal!),
                hintText: hintText,
                hintStyle: const TextStyle(
                  color: TColor.tabColors,
                ),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
              ),
              validator: customValidator ??
                  (value) {
                    try {
                      if (value!.isEmpty) {
                        debugPrint(validation);
                        return validation;
                      } else if (value.length < requiredNumber!) {
                        return "this field should be more than $requiredNumber characters long";
                      }
                      return null;
                    } catch (e, stackTrace) {
                      debugPrint("catch error at Absence repo: $e");
                      debugPrint(stackTrace.toString());
                      return "not valid";
                    }
                  },
              onSaved: saved,
              obscureText: security,
              maxLines: maxLine,
              keyboardType: inputType,
            ),
          ),
        ),
      ],
    );
  }
}
