// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class AppStrings {
  static const home = 'home';
  static const login = 'login';
  static const religion = 'Religion';
  static const type = 'type';
  static const selectAbsenceDate = 'select_absence_date';
  static const currentPassword = 'current_password';
  static const newAddress = 'NewAddress';
  static const show = 'show';
  static const email = 'email';
  static const password = 'password';
  static const forgetPassword = 'forgetPassword';
  static const remember = 'remember';
  static const notHaveAccount = 'notHaveAccount';
  static const createAccount = 'createAccount';
  static const newPassword = 'newPassword';
  static const againPassword = 'againPassword';
  static const changePassword = 'changePassword';
  static const createPassword = 'createPassword';
  static const signup = 'signup';
  static const name = 'name';
  static const phoneNumber = 'phoneNumber';
  static const confirmPassword = 'confirmPassword';
  static const haveAccount = 'haveAccount';
  static const forget = 'forget';
  static const sendCodeRegister = 'sendCodeRegister';
  static const sendCodeAgain = 'sendCodeAgain';
  static const passwordChangeSuccess = 'passwordChangeSuccess';
  static const goHome = 'goHome';
  static const oldPassword = 'oldPassword';
  static const save = 'save';
  static const sendCode = 'sendCode';
  static const next = 'next';
  static const add = 'add';
  static const addSon = 'addSon';
  static const code = 'code';
  static const sonData = 'sonData';
  static const sonLocation = 'sonLocation';
  static const showSonOnMap = 'showSonOnMap';
  static const requestAbsence = 'requestAbsence';
  static const date = 'date';
  static const requestChangeAddress = 'requestChangeAddress';
  static const yourAddresses = 'yourAddresses';
  static const addManualAddress = 'addManualAddress';
  static const addAddressFromMap = 'addAddressFromMap';
  static const addNewAddress = 'addNewAddress';
  static const country = 'country';
  static const nameSon = 'nameSon';
  static const nameParent = 'nameParent';
  static const cityOrRegin = 'cityOrRegin';
  static const streetName = 'streetName';
  static const phase = 'phase';
  static const addAddress = 'addAddress';
  static const setting = 'setting';
  static const profile = 'profile';
  static const languages = 'languages';
  static const help = 'help';
  static const english = 'english';
  static const arabic = 'arabic';
  static const updateProfile = 'updateProfile';
  static const bloodType = 'bloodType';
  static const city = 'city';
  static const children = 'childrens';
  static const stage = 'stage';
  static const absenteeismRequests = 'absenteeismRequests';
  static const searchingAddressOnMap = 'searchingAddressOnMap';
  static const logout = 'logout';
  static const validEmail = 'validEmail';
  static const validPhone = 'validPhone';
  static const validName = 'validName';
  static const validAddress = 'validAddress';
  static const validPassword = 'validPassword';
  static const validConfirmPassword = 'validConfirmPassword';
  static const checkPassword = 'checkPassword';
  static const checkEmail = 'checkEmail';
  static const emailVerified = 'email_verified';
  static const wrongCode = 'wrong_code';
  static const address = 'address';
  static const sons = 'sons';
  static const settings = 'settings';
  static const absenceRequests = 'absence_requests';
  static const notifications = 'notifications';
  static const getLocations = 'getLocations';
  static const locationDone = 'locationDone';
  static const setLocation = 'setLocation';
  static const morningTrip = 'morning_trip';
  static const eveningTrip = 'evening_trip';
  static const fullDay = 'full_day';
  static const grade = 'grade';
  static const gradeEdu = 'grade_edu';
  static const birthDate = 'birth_date';
  static const school = 'school';
  static const addNewRequest = 'add_new_request';
  static const chooseStudent = 'choose_student';
  static const chooseTripType = 'choose_trip_type';
  static const locationOnMap = 'location_on_map';
  static const sendSuccessfully = 'send_successfully';
  static const sendFailed = 'send_failed';
  static const send = 'send';
  static const notFound = 'not_found';
  static const isRequired = 'is_required';
  static const bus = 'bus';
  static const pro = 'pro';
  static const coupon = 'coupon';
  static const enterCoupon = 'enter_coupon';
  static const subscribe = 'subscribe';
  static const benefits = 'benefits';
  static const subscribeWithCoupon = 'subscribe_with_coupon';
  static const or = 'or';
  static const location = 'location';
  static const delete = 'delete';
  static const goodMorning = 'good_morning';
  static const goodEvening = 'good_evening';
  static const subscribedSuccessfully = 'subscribed_successfully';
  static const and = 'and';
  static const notMatch = 'not_match';
  static const noTrips = 'no_trips';
  static const alreadySubscribed = 'already_subscribed';
  static const yes = 'yes';
  static const no = 'no';
  static const search = 'search';
  static const successfullyDone = 'successfully_done';
  static const tripLoading = 'trip_loading';
  static const alreadyRequest = 'already_request';
  static const permanentAddressChange = 'permanent_address_change';
  static const temporaryAddressChange = 'temporary_address_change';
  static const selectAddressChangeType = 'select_address_change_type';
  static const dateRange = 'date_range';
  static const startDate = 'start_date';
  static const endDate = 'end_date';
  static const submitRequest = 'submit_request';
  static const tempAddressChangeSuccess = 'temp_address_change_success';
  static const startDateAfterToday = 'start_date_after_today';
  static const endDateAfterToday = 'end_date_after_today';
  static const endDateAfterStartDate = 'end_date_after_start_date';
  static const openTrips = 'openTrips';
  static const tripTypes = 'tripTypes';
  static const supervisorName = 'supervisorName';
  static const busNumber = 'busNumber';
  static const busName = 'bus_name';
  static const student = 'student';
  static const all = 'all';
  static const unRead = 'unRead';
  static const benefitBusatyBro = 'benefitBusatyBro';
  static const withoutAds = 'withoutAds';
  static const trackingSonInMoment = 'trackingSonInMoment';
  static const tackingBuysInMoment = 'tackingBuysInMoment';
  static const checkInternetConnection = 'checkInternetConnection';
  static const deleteAccountTitle = 'deleteAccountTitle';
  static const deleteAccountConfirm = 'deleteAccountConfirm';
  static const deleteAccountNote = 'deleteAccountNote';
  static const cancel = 'cancel';
  static const deleteAccount = 'deleteAccount';
  static const contactUs = 'contact_us';
  static const getInTouch = 'get_in_touch';
  static const wedLoveToHear = 'wed_love_to_hear';
  static const enterYourName = 'enter_your_name';
  static const enterYourEmail = 'enter_your_email';
  static const describeProblem = 'describe_problem';
  static const pleaseEnterName = 'please_enter_name';
  static const pleaseEnterEmail = 'please_enter_email';
  static const pleaseValidEmail = 'please_valid_email';
  static const pleaseDescribeProblem = 'please_describe_problem';
  static const messageTooLong = 'message_too_long';
  static const contactDirectly = 'contact_directly';
  static const copy = 'copy';
  static const emailCopied = 'email_copied';
  static const sending = 'sending';
  static const emailSent = 'email_sent';
  static const failedToSend = 'failed_to_send';
  static const privacyPolicy = 'privacy_policy';
  static const privacyPolicyTitle = 'privacy_policy_title';
  static const privacyPolicyContent = 'privacy_policy_content';
  static const playStore = 'play_store';
  static const rateApp = 'rate_app';
  static const shareApp = 'share_app';
  static const moreApps = 'more_apps';
  static const wrongEmail = 'wrong_email';
  static const String subscribeToTrack = 'subscribe_to_track';
  static const String subscribeNow = 'subscribe_now';
  static const String trackingDescription = 'trackingDescription';
  static const String termsAndConditions = 'terms_and_conditions';
  static const loginWithGoogle = 'loginWithGoogle';
  static const String checkConnection = 'checkConnection';
  static const String accountDisabled = 'accountDisabled';
  static const String loginFailed = 'loginFailed';
  static const String updateRequired = "update_required";
  static const String updateMessage = "update_message";
  static const String update = "update";

  // Complete Profile Screen Keys
  static const completeProfile = 'completeProfile';
  static const uploadPhoto = 'uploadPhoto';
  static const submit = 'submit';
  static const somethingWentWrong = 'somethingWentWrong';
  static const profileCompleted = 'profileCompleted';
  static const pleaseCompleteProfile = 'pleaseCompleteProfile';
  static const pleaseUploadPhoto = 'pleaseUploadPhoto';
  static const pleaseEnterValidPhone = 'pleaseEnterValidPhone';
  static const pleaseEnterValidName = 'pleaseEnterValidName';
  static const pleaseEnterValidAddress = 'pleaseEnterValidAddress';
  static const personalInformation = 'personalInformation';
}
