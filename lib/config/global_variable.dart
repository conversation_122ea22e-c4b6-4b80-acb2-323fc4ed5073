import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:flutter/material.dart';

String? token;
String? tempToken;
String? fCMToken;
String? userName;
String? userEmail;
String? userImageUrl;
String? userAddress;
String? userPhone;
bool? subscriptionStatus;
String? authMethod;

// Subscription details
String? subscriptionPlanName;
String? subscriptionAmount;
String? subscriptionStartDate;
String? subscriptionEndDate;
String? subscriptionPaymentMethod;
String? subscriptionStatusText;

// Constants for authentication method
const String authMethodKey = "auth_method";
const String authMethodGoogle = "google";
const String authMethodRegular = "regular";
// e8UtlROiQ82K_6Bc2FCZ-0:APA91bGdtxztAlFqx1Aw7AGne7trcSo4ZHTbdNU70XHKkk_yuxUpI-LKzY83l8cHHXNccEZfD_kiChSMHX_al58P4K9nY3QELDVmJcE-uBk2ZBMxgvTD7dQ

// const String socketToken = "****************************************";
const String socketToken = "71e456b873f87f214f139799878b911a";
const String firebaseServerKey =
    "AAAAD2sLMEM:APA91bHKGl6SMCq8kG9YMf0TZV5L_h13xBlc2iGDwcY8pqUPy6mh5HF4Q1ctRYBQP9lOrZfcW4gL-Ii0sMIl1BmoYizP2x5LMKJgN6ghjRq9sO5rf46tpNG_6zDOd_EXm6qUxR_NljXo";

final snackBarKey = GlobalKey<ScaffoldMessengerState>();

final navigatorKey = GlobalKey<NavigatorState>();

void initializeFCMToken() async {
  fCMToken = CacheHelper.getString("fcmToken");
  debugPrint("---------------------------------------------------------------");
  debugPrint("fcmToken: $fCMToken");
  debugPrint("---------------------------------------------------------------");
}
