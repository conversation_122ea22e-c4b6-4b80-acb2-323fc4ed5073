import 'package:flutter_test/flutter_test.dart';
import 'package:busaty_parents/utils/email_validator.dart';

void main() {
  group('EmailValidator Tests', () {
    test('should accept valid email addresses with + symbol', () {
      // Test cases with + symbol (email aliasing)
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(
          EmailValidator.validateEmail('<EMAIL>'), isNull);
    });

    test('should accept standard valid email addresses', () {
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
    });

    test('should reject invalid email addresses', () {
      expect(EmailValidator.validateEmail(''), isNotNull);
      expect(EmailValidator.validateEmail('invalid-email'), isNotNull);
      expect(EmailValidator.validateEmail('user@'), isNotNull);
      expect(EmailValidator.validateEmail('@domain.com'), isNotNull);
      expect(EmailValidator.validateEmail('user@@domain.com'), isNotNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNotNull);
      expect(EmailValidator.validateEmail('.<EMAIL>'), isNotNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNotNull);
    });

    test('should handle null input', () {
      expect(EmailValidator.validateEmail(null), isNotNull);
    });

    test('should handle whitespace', () {
      expect(EmailValidator.validateEmail('  <EMAIL>  '), isNull);
      expect(EmailValidator.validateEmail(' '), isNotNull);
    });

    test('Arabic validation should work correctly', () {
      expect(
          EmailValidator.validateEmailArabic('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmailArabic(''), isNotNull);
      expect(EmailValidator.validateEmailArabic(null), isNotNull);
      expect(EmailValidator.validateEmailArabic('invalid-email'), isNotNull);
    });

    test('should handle plus sign edge cases correctly', () {
      // Invalid: + at the end of local part
      expect(EmailValidator.validateEmail('<EMAIL>'), isNotNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNotNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNotNull);

      // Valid: + with text after it
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);

      // Valid: multiple + signs with text
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
    });
  });
}
