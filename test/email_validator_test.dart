import 'package:flutter_test/flutter_test.dart';
import 'package:busaty_parents/utils/email_validator.dart';

void main() {
  group('EmailValidator Tests', () {
    test('should accept valid email addresses with + symbol', () {
      // Test cases with + symbol (email aliasing)
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(
          EmailValidator.validateEmail('<EMAIL>'), isNull);
    });

    test('should accept standard valid email addresses', () {
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNull);
    });

    test('should reject invalid email addresses', () {
      expect(EmailValidator.validateEmail(''), isNotNull);
      expect(EmailValidator.validateEmail('invalid-email'), isNotNull);
      expect(EmailValidator.validateEmail('user@'), isNotNull);
      expect(EmailValidator.validateEmail('@domain.com'), isNotNull);
      expect(EmailValidator.validateEmail('user@@domain.com'), isNotNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNotNull);
      expect(EmailValidator.validateEmail('.<EMAIL>'), isNotNull);
      expect(EmailValidator.validateEmail('<EMAIL>'), isNotNull);
    });

    test('should reject each invalid email individually', () {
      print('Testing empty string: ${EmailValidator.validateEmail('')}');
      print(
          'Testing invalid-email: ${EmailValidator.validateEmail('invalid-email')}');
      print('Testing user@: ${EmailValidator.validateEmail('user@')}');
      print(
          'Testing @domain.com: ${EmailValidator.validateEmail('@domain.com')}');
      print(
          'Testing user@@domain.com: ${EmailValidator.validateEmail('user@@domain.com')}');
      print(
          'Testing <EMAIL>: ${EmailValidator.validateEmail('<EMAIL>')}');
      print(
          'Testing .<EMAIL>: ${EmailValidator.validateEmail('.<EMAIL>')}');
      print(
          'Testing <EMAIL>: ${EmailValidator.validateEmail('<EMAIL>')}');
    });

    test('should handle null input', () {
      expect(EmailValidator.validateEmail(null), isNotNull);
    });

    test('should handle whitespace', () {
      expect(EmailValidator.validateEmail('  <EMAIL>  '), isNull);
      expect(EmailValidator.validateEmail(' '), isNotNull);
    });

    test('Arabic validation should work correctly', () {
      expect(
          EmailValidator.validateEmailArabic('<EMAIL>'), isNull);
      expect(EmailValidator.validateEmailArabic(''), isNotNull);
      expect(EmailValidator.validateEmailArabic(null), isNotNull);
      expect(EmailValidator.validateEmailArabic('invalid-email'), isNotNull);
    });
  });
}
